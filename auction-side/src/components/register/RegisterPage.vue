<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import { useRegister } from './composables/useRegister'
import ConfirmData from './ConfirmData.vue'
import EntryForm from './EntryForm.vue'

const BreadCrumb = defineAsyncComponent(
  () => import(/* webpackChunkName: "BreadCrumb" */ '@/components/common/BreadCrumb.vue')
)

const {
  step,
  constants,
  errorMsg,
  emailLangList,
  prevStep,
  sendConfirmRequest,
  sendRequest,
  handleErrorMsg,
  t
} = useRegister()
</script>
<template>
  <BreadCrumb :customTitle="t('register.title')" />
  <section id="entry">
    <h1 class="mb0">{{ t('register.title') }}</h1>
    <div class="container">
      <EntryForm
        v-if="step === 1"
        :constants="constants"
        :errorMsg="errorMsg"
        @confirm-inputs="sendConfirmRequest"
        @update:errorMsg="handleErrorMsg"
        :emailLangOptions="emailLangList"
      />
      <ConfirmData
        v-else-if="step === 2"
        :constants="constants"
        :errorMsg="errorMsg"
        @back="prevStep"
        @regist-member="sendRequest"
        :emailLangOptions="emailLangList"
      />
    </div>
  </section>
</template>
