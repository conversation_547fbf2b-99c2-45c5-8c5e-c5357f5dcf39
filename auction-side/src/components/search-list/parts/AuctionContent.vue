<script setup>
import { computed, defineEmits, defineProps } from 'vue'
import { useRoute } from 'vue-router'
import { useLocale } from 'vuetify'
import { localeString2Number, priceLocaleString } from '../../../composables/common'
import useSearchProducts from '../../../composables/searchProducts'
import useBidBulk from '../../../composables/useBidBulk'
import { CLASSIFICATIONS, PATH_NAME } from '../../../defined/const'
import { useSearchResultStore } from '../../../stores/search-results'

const emit = defineEmits(['refresh'])
const props = defineProps(['productList', 'exhibitionInfo', 'isAscendingAuction'])

const { t: translate } = useLocale()
const route = useRoute()
const { bidBulkHandle } = useBidBulk()
const searchStore = useSearchResultStore()
const { searchScope, countUpViewMore } = useSearchProducts()

const isShowLoadMore = computed(() => {
  return (
    props.exhibitionInfo?.count &&
    props.productList &&
    props.productList.length < props.exhibitionInfo.count
  )
})

const localProductList = computed(() => props.productList)
const isShowBulkBid = computed(() => {
  return (
    (route.path === PATH_NAME.MYPAGE_FAVORITE || route.path === PATH_NAME.MYPAGE_BIDDING) &&
    !props.isAscendingAuction
  )
})
const isShowTotalBidPrice = computed(() => {
  return route.path === PATH_NAME.MYPAGE_FAVORITE || route.path === PATH_NAME.MYPAGE_BIDDING
})

const totalInputBidPrice = computed(() => {
  // Only add up the bid price and bid quantity if there is already a bid price and bid quantity
  const sum = props.productList.reduce((acc, cur) => {
    // If there is an error in the bid price or bid quantity, do not add to the total
    if (!cur.bid_status.bid_price || !cur.bid_status.bid_quantity) {
      return acc
    }
    // No problem, add to the total
    return (
      acc +
      (cur.bid_status.bid_price && cur.bid_status.bid_quantity
        ? localeString2Number(cur.bid_status.bid_price) *
          localeString2Number(cur.bid_status.bid_quantity)
        : 0)
    )
  }, 0)
  return priceLocaleString(sum)
})

const loadMore = () => {
  countUpViewMore()
  searchScope({
    category: searchStore.searchCategory,
    favorite: searchStore.favorite,
    exhibitionNos: [props.exhibitionInfo.exhibition_no],
    auction_classification: props.isAscendingAuction
      ? CLASSIFICATIONS.ASCENDING
      : CLASSIFICATIONS.SEALED
  })
}

const handleFavorite = () => {
  console.log('favorite')
  emit('refresh')
}

const bidBulk = () => {
  console.log('bulk bid')
  bidBulkHandle({
    exhibitionNo: props.exhibitionInfo.exhibition_no
  })
}
</script>
<template>
  <div class="auction-contents">
    <div v-if="isShowBulkBid" class="wrap-btn bulkbid">
      <button class="bulkbid modal-open" @click="bidBulk">
        {{ translate('bulkBid.bulkBidButton') }}
      </button>
    </div>
    <div class="status">
      <div class="num-results">
        <span>{{ exhibitionInfo?.count }}</span
        >{{ translate('filterBox.auctionCount') }}
      </div>
    </div>

    <div class="list-item-table">
      <table
        :class="{
          'mypage-list': route.path !== PATH_NAME.TOP,
          favorite: route.path === PATH_NAME.MYPAGE_FAVORITE
        }"
      >
        <thead class="item-header">
          <slot name="header"></slot>
        </thead>
        <tbody>
          <template v-for="item in localProductList" :key="item.itemNo">
            <slot
              name="item"
              :item="item"
              :exhibitionInfo="exhibitionInfo"
              :favorite="handleFavorite"
            ></slot>
          </template>
        </tbody>
      </table>
      <div v-if="isShowLoadMore" class="wrap-btn list-more">
        <button class="btn" @click="loadMore">
          <span class="txt">{{ translate('common.more') }}<span class="arrow"></span></span>
        </button>
      </div>
      <!-- 入札合計金額 -->
      <div>
        <div v-if="isShowTotalBidPrice" class="table-subtotal-total favorite">
          <p class="auction-title">{{ exhibitionInfo.exhibition_name }}</p>
          <p class="price-subtotal">
            <span class="label">{{ translate('productDetail.bidTotalPrice') }}</span>
            <span class="unit">$</span><span class="val">{{ totalInputBidPrice }}</span>
          </p>
        </div>
        <div v-if="isShowBulkBid" class="wrap-btn bulkbid">
          <button class="bulkbid modal-open" @click="bidBulk">
            {{ translate('bulkBid.bulkBidButton') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
