<script setup lang="ts">
import ConfirmData from '@/components/ConfirmData.vue'
import EntryForm from '@/components/EntryForm.vue'
import { useChangeMember } from './composables/useChangeMember.ts'

const {
  step,
  constants,
  errorMsg,
  emailLangList,
  prevStep,
  sendConfirmRequest,
  sendRequest,
  showWithdrawalConfirmDialog,
  handleErrorMsg
} = useChangeMember()
</script>
<template>
  <div class="container">
    <EntryForm
      v-if="step === 1"
      :constants="constants"
      :errorMsg="errorMsg"
      @confirm-inputs="sendConfirmRequest"
      @withdrawal="showWithdrawalConfirmDialog"
      @update:errorMsg="handleErrorMsg"
      :emailLangOptions="emailLangList"
    />
    <ConfirmData
      v-else-if="step === 2"
      :constants="constants"
      :errorMsg="errorMsg"
      @back="prevStep"
      @regist-member="sendRequest"
      :emailLangOptions="emailLangList"
    />
  </div>
</template>
