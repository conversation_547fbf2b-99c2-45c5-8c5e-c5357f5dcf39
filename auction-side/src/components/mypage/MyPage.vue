<script setup lang="ts">
import BreadCrumb from '@/components/common/BreadCrumb.vue'
import useMyPageBreadcrumb from '@/composables/useMyPageBreadcrumb'
import MyPageNavigation from './MyPageNavigation.vue'

// Simple breadcrumb logic
const { breadcrumbTitle } = useMyPageBreadcrumb()
</script>

<template>
  <main id="main" class="mypage">
    <BreadCrumb :custom-title="breadcrumbTitle" />
    <h2 class="page-ttl mypage">
      <p class="ttl">マイページ</p>
      <p class="sub">Mypage</p>
    </h2>

    <!-- Navigation component handles all tab logic -->
    <MyPageNavigation />

    <!-- Router view renders the active child component -->
    <RouterView />
  </main>
</template>
