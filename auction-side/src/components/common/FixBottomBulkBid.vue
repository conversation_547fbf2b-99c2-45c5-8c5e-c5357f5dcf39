<script setup>
import $ from 'jquery'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useLocale } from 'vuetify'
import useBidBulk from '../../composables/useBidBulk'
import { PATH_NAME } from '../../defined/const'

const { t: translate } = useLocale()
const route = useRoute()
const { bidBulkHandle } = useBidBulk()

const bidBulk = () => {
  console.log('bulk bid')
  bidBulkHandle()
}

const fixButton = ref(null)
const showFixButton = ref(false)

const handleScroll = () => {
  const footer = $('footer')
  const scrollTop = window.scrollY // Current scroll position
  const windowHeight = window.innerHeight // Window height
  const footerOffset = footer.offset().top // Footer position

  // Show/hide button based on scroll position
  if (scrollTop > 100 && scrollTop + windowHeight < footerOffset) {
    showFixButton.value = true
  } else {
    showFixButton.value = false
  }
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
<template>
  <div ref="fixButton" class="fix-bottom-contents" v-show="showFixButton">
    <div class="cont-wrap wrap-btn">
      <p v-html="translate('bulkBid.note')"></p>
      <button class="btnBsc-bulkbid modal-open" @click="bidBulk">
        {{
          translate(
            route.path === PATH_NAME.MYPAGE_BIDDING
              ? 'bulkBid.bulkReBidButton'
              : 'bulkBid.bulkBidButton'
          )
        }}
      </button>
    </div>
  </div>
</template>
