<script setup>
defineOptions({
  name: 'PasswordReminder'
})

import { computed, ref } from 'vue'
import { useLocale } from 'vuetify'
import useApi from '../../composables/useApi'
import { PATTERN } from '../../defined/const'
import BreadCrumb from '../common/BreadCrumb.vue'
import ReminderComplete from './ReminderComplete.vue'

const { apiExecute, parseHtmlResponseError } = useApi()

const email = ref('')
const confirmEmail = ref('')
const errorMsg = ref('')
const { t: translate } = useLocale()

const checkInput = computed(() => {
  const check = {
    email: { pass: false, errMsg: translate('login.reminderEmailCheckError') },
    confirmEmail: { pass: false, errMsg: translate('login.reminderConfirmEmailCheckError') }
  }
  if (PATTERN.EMAIL.test(email.value)) {
    check.email.pass = true
    check.email.errMsg = ''
  }
  if (PATTERN.EMAIL.test(confirmEmail.value) && email.value === confirmEmail.value) {
    check.confirmEmail.pass = true
    check.confirmEmail.errMsg = ''
  }
  return check
})

const completed = ref(false)

/**
 *
 */
const sendRequest = async () => {
  console.log('forgot password request12!!')
  // Mock the async request delay
  await new Promise((resolve) => setTimeout(resolve, 500))
  completed.value = true

  // const requestData = { user_id: null, email: email.value }
  // await apiExecute(API_PATH.REISSUE_PASSWORD, requestData)
  //   .then((data) => {
  //     completed = true
  //   })
  //   .catch((error) => {
  //     errorMsg.value = parseHtmlResponseError(error)?.errorMessage
  //     console.log({ errorMsg })
  //   })
}
</script>

<template>
  <main id="main" class="reminder">
    <ReminderComplete v-if="completed" />
    <div v-else>
      <BreadCrumb customTitle="パスワードをお忘れの方" />
      <h2 class="page-ttl">
        <p class="ttl">パスワードをお忘れの方</p>
        <p class="sub">Reminder</p>
      </h2>
      <div class="container">
        <div class="remind-msg">
          <p class="remind-txt-att">
            <span> パスワードをお忘れの方は登録したIDとメールアドレスを入力してください。</span>
            <span>「送信」ボタンを押しますと、パスワードが登録メールアドレスに届きます。</span>
          </p>
          <p class="mt15 remind-txt-att">
            <span>IDをお忘れの場合は、<a>お問い合わせフォーム</a>からお問い合わせください。</span>
          </p>
        </div>
        <section id="login-form">
          <form>
            <table class="tbl-login">
              <tbody>
                <tr>
                  <th>ID</th>
                  <td>
                    <input type="text" class="ime-dis" placeholder="半角英数字" required />
                  </td>
                </tr>
                <tr>
                  <th>メールアドレス</th>
                  <td>
                    <input type="email" class="ime-dis" required />
                  </td>
                </tr>
                <tr>
                  <th>メールアドレス(確認用)</th>
                  <td>
                    <input type="email" class="ime-dis" required />
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="btn-form">
              <input type="button" id="sbm-login" value="送信する" @click="sendRequest" />
            </div>
          </form>
        </section>
      </div>
    </div>
  </main>
</template>

<style scoped>
.work-break {
  word-break: break-word;
}
</style>
