<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLocale } from 'vuetify'
import useSearchProducts from '../../composables/searchProducts.js'
import { PATH_NAME } from '../../defined/const.js'
import { useAuthStore } from '../../stores/auth.js'
import { useCommonConstantsStore } from '../../stores/common-constants.js'
import useChangeLanguage, { useLanguageStore } from '../../stores/language.js'
import { useSearchResultStore } from '../../stores/search-results.js'

const { t: translate, current } = useLocale()
const route = useRoute()
const router = useRouter()
const auth = useAuthStore()
const { search } = useSearchProducts()
const searchStore = useSearchResultStore()
const commonConstants = useCommonConstantsStore()
const localSearchKey = ref('')
const languageStore = useLanguageStore()
const { changeLanguage } = useChangeLanguage()

// Get category list from store
const categoryList = computed(() => commonConstants.categoryList || [])

// Handle category selection
const selectCategory = async (categoryValue) => {
  if (route.path !== PATH_NAME.TOP) {
    await router.push(PATH_NAME.TOP)
  }
  
  searchStore.categoryList = [categoryValue]
  await search({
    searchKey: searchStore.searchKeyTop,
    categoryList: [categoryValue],
    modelList: [],
    unSoldOut: false
  })
}

// Handle search
const handleHeaderSearch = async () => {
  if (route.path !== PATH_NAME.TOP) {
    await router.push(PATH_NAME.TOP)
  }

  searchStore.setSearchKeyTop(localSearchKey.value)
  searchStore.categoryList.length = 0
  await search({
    searchKey: searchStore.searchKeyTop,
    categoryList: [],
    modelList: [],
    unSoldOut: false
  })

  // SP: SlideUP the gNav after category selected
  $('header .gNav').slideUp()
  $('.btnMenu').removeClass('close')
}

// Handle language change
const handleChangeLanguage = (event) => {
  changeLanguage(event.target.value)
}

watch(
  () => searchStore.searchKey,
  (isNewVal) => {
    localSearchKey.value = isNewVal
  }
)

onMounted(() => {
  // Toggle navigation menu on mobile
  $('header p.btnMenu').on('click', function () {
    $('header .gNav').slideToggle()
    $(this).toggleClass('close')
  })

  // SP Nav child open/close in Header
  $('header .gNav nav ul.only_sp li p').on('click', function () {
    $(this).next('ul').slideToggle()
    $(this).toggleClass('close')
  })
  // Hide the gNav after clicking the most child element
  $('header .gNav nav ul.only_sp li ul li').on('click', () => {
    $('header .gNav').slideUp()
    $('.btnMenu').removeClass('close')
  })
})
</script>

<template>
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="h-top">
        <p class="btnMenu only_sp"><span class="ham"></span></p>
        <h1 class="h-top-logo">
          <router-link class="logo" :to="PATH_NAME.TOP">
            <img src="@/assets/img/common/logo_cecauction.png" alt="CEC AUCTION" />
          </router-link>
        </h1>
        <div class="h-top-menu only_sp">
          <ul>
            <li>
              <RouterLink class="btn-favorite" :to="PATH_NAME.MYPAGE_FAVORITE">
                <img src="@/assets/img/common/icn_header_favorite.svg" />
              </RouterLink>
            </li>
            <li>
              <RouterLink class="btn-member" :to="PATH_NAME.LOGIN">
                <img src="@/assets/img/common/icn_nav_member.svg" />
              </RouterLink>
            </li>
          </ul>
        </div>
      </div>
      <div class="nav-elm">
        <div class="search-elm only_pc">
          <div class="search-category">
            <li>
              <a href="#" class="nav-label">{{ translate('top.appBar.selectCategory') }}</a>
              <div class="menu-list">
                <p class="arrow-box"></p>
                <div class="panel-wrap">
                  <div class="category-box">
                    <div class="category-all">
                      <p><a @click="selectCategory('')">すべてのカテゴリー</a></p>
                      <p><a @click="selectCategory('ascending')">競り上がり式オークション</a></p>
                      <p><a @click="selectCategory('sealed')">封印入札式オークション</a></p>
                    </div>
                  </div>
                  <div class="category-box" v-if="categoryList.length > 0">
                    <div class="category-top">
                      <p><a>カテゴリー</a></p>
                    </div>
                    <div class="category-secondary">
                      <ul class="list-secondary">
                        <li v-for="cat in categoryList" :key="cat.value1">
                          <a @click="selectCategory(cat.value1)">{{ cat.value2 }}</a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </div>
          <div class="search-keyword">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="side-search-keyword search-keyword"
              v-model="localSearchKey"
              :placeholder="translate('filterBox.inputPlaceholder')"
            />
            <button @click="handleHeaderSearch()">
              <img src="@/assets/img/common/icn_search_gray.svg" />
            </button>
          </div>
          <div class="info-menu">
            <li>
              <a href="#" class="nav-label">{{ translate('guidance.aboutSite') || 'サイトについて' }}</a>
              <div class="menu-list">
                <p class="arrow-box"></p>
                <ul>
                  <li><RouterLink :to="PATH_NAME.GUIDE">{{ translate('guidance.firstTime') || '初めての方へ' }}</RouterLink></li>
                  <li><RouterLink :to="PATH_NAME.GUIDE">{{ translate('guidance.shoppingGuide') || 'ショッピングガイド' }}</RouterLink></li>
                  <li><RouterLink :to="PATH_NAME.FAQ || '#'">{{ translate('guidance.faq') || 'よくあるご質問' }}</RouterLink></li>
                  <li><RouterLink :to="PATH_NAME.INQUIRY">{{ translate('guidance.inquiry') || 'お問い合わせ' }}</RouterLink></li>
                  <li><RouterLink :to="PATH_NAME.GUIDE">{{ translate('guidance.aboutMembership') || '会員サービスについて' }}</RouterLink></li>
                </ul>
              </div>
            </li>
          </div>
        </div>
        <ul class="nav-btn only_pc">
          <li v-if="auth.isAuthenticated" class="nav-mypage favorite">
            <RouterLink :to="PATH_NAME.MYPAGE_FAVORITE">
              <img src="@/assets/img/common/icn_header_favorite.svg" />
              <span>{{ translate('favorite.title') || 'お気に入り' }}</span>
            </RouterLink>
          </li>
          <li v-if="auth.isAuthenticated" class="nav-mypage bid">
            <RouterLink :to="PATH_NAME.MYPAGE_BIDDING">
              <img src="@/assets/img/common/icn_bid.svg" class="bid" />
              <span>{{ translate('auction.bidOngoing') || '入札中' }}</span>
            </RouterLink>
          </li>
          <li v-if="auth.isAuthenticated" class="nav-mypage bidded">
            <RouterLink :to="PATH_NAME.MYPAGE_BID_HISTORY">
              <img src="@/assets/img/common/icn_bidded.svg" class="bidded" />
              <span>{{ translate('auction.bidHistory') || '落札履歴' }}</span>
            </RouterLink>
          </li>
          <li v-if="!auth.isAuthenticated" class="nav-account registration">
            <RouterLink :to="PATH_NAME.REGISTER">
              <span>{{ translate('top.appBar.register') || '新規会員登録' }}</span>
            </RouterLink>
          </li>
          <li v-if="!auth.isAuthenticated" class="nav-account login">
            <RouterLink :to="PATH_NAME.LOGIN">
              <span>{{ translate('top.appBar.login') || 'ログイン' }}</span>
            </RouterLink>
          </li>
          <li v-if="auth.isAuthenticated" class="nav-mypage login-out">
            <a @click="auth.showLogoutMessage" class="cursor-pointer">
              <img src="@/assets/img/common/icn_nav_logout.svg" />
              <span>{{ translate('top.appBar.logout') || 'ログアウト' }}</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <!-- gNav PC/SP end -->
  </header>
</template>

<style lang="css" scoped>
header .wrap-header-nav-elm .gnavi__wrap .gnavi__lists .gnavi__list a.arrow:before {
  box-sizing: content-box;
}
header .wrap-header-elm .nav-elm .lang-wrap:after {
  box-sizing: content-box;
}
.break-line {
  word-break: break-word;
}
</style>
