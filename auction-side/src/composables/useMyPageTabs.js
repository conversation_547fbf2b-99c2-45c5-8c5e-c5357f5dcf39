import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { PATH_NAME } from '@/defined/const'

// Import child components
import MyPageAccount from '@/components/mypage/account/_MyPageAccount.vue'
import MyPageBidding from '@/components/mypage/bidding/_MyPageBidding.vue'
import MyPageFavorite from '@/components/mypage/favorite/_MyPageFavorite.vue'
import MyPageHistory from '@/components/mypage/history/_MyPageHistory.vue'

export default function useMyPageTabs() {
  const route = useRoute()
  const router = useRouter()

  // Tab configuration
  const tabs = [
    {
      key: 'favorite',
      label: 'お気に入り',
      icon: 'favorite',
      path: PATH_NAME.MYPAGE_FAVORITE,
      component: MyPageFavorite,
      breadcrumbTitle: 'お気に入り'
    },
    {
      key: 'bidding',
      label: '入札中',
      icon: 'bidding',
      path: PATH_NAME.MYPAGE_BIDDING,
      component: MyPageBidding,
      breadcrumbTitle: '入札中'
    },
    {
      key: 'history',
      label: '落札履歴',
      icon: 'winning-history',
      path: PATH_NAME.MYPAGE_BID_HISTORY,
      component: MyPageHistory,
      breadcrumbTitle: '落札履歴'
    },
    {
      key: 'account',
      label: '会員情報編集',
      icon: 'account',
      path: PATH_NAME.MYPAGE_ACCOUNT,
      component: MyPageAccount,
      breadcrumbTitle: '会員情報編集',
      hasUploadIcon: true
    }
  ]

  // Get current active tab based on route
  const activeTab = computed(() => {
    const currentPath = route.path
    return tabs.find(tab => tab.path === currentPath) || tabs[0]
  })

  // Get current component to render
  const currentComponent = computed(() => activeTab.value.component)

  // Get breadcrumb title
  const breadcrumbTitle = computed(() => activeTab.value.breadcrumbTitle)

  // Handle tab navigation
  const navigateToTab = (tab) => {
    if (route.path !== tab.path) {
      router.push(tab.path)
    }
  }

  // Check if tab is active
  const isTabActive = (tab) => {
    return route.path === tab.path
  }

  // Get tab by key
  const getTabByKey = (key) => {
    return tabs.find(tab => tab.key === key)
  }

  // Get tab by path
  const getTabByPath = (path) => {
    return tabs.find(tab => tab.path === path)
  }

  return {
    tabs,
    activeTab,
    currentComponent,
    breadcrumbTitle,
    navigateToTab,
    isTabActive,
    getTabByKey,
    getTabByPath
  }
}
