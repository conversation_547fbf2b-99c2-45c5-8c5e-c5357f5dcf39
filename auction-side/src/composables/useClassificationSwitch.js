import { CLASSIFICATIONS } from '@/defined/const'
import { computed, ref } from 'vue'

/**
 * Composable for managing classification switching between ascending and sealed auction types
 */
export default function useClassificationSwitch(defaultClassification = CLASSIFICATIONS.ASCENDING) {
  const classification = ref(defaultClassification)

  const isAscending = computed(() => classification.value === CLASSIFICATIONS.ASCENDING)
  const isSealed = computed(() => classification.value === CLASSIFICATIONS.SEALED)

  const getClassificationLabel = (classificationType) => {
    return classificationType === CLASSIFICATIONS.ASCENDING ? '競り上がり入札' : '封印入札'
  }

  // Get classification display labels (TODO maybe no need)
  const currentLabel = computed(() => getClassificationLabel(classification.value))

  return {
    classification,
    isAscending,
    isSealed,
    currentLabel
  }
}
